import { PaymentMethod } from '@woo/services/payments';
import { OrderSteps } from '@woo/services/storefront';
import wooPlugin from '@woo/WooPlugin';

export default class MbwayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.clickApproveButton();
    }

    /**
     * Clicks the Approve button for MBWay payment.
     */
    protected async clickApproveButton(): Promise<void> {
        await wooPlugin.helper.waitForVisibleAndClick('paymentMethods.mbway.approveButton', 'Clicking MBWay Approve button');
        console.log('MBWay Approve button clicked.');
    }

    /**
     * Clicks the Deny button for MBWay payment (for negative scenarios).
     */
    async clickDenyButton(): Promise<void> {
        await wooPlugin.helper.waitForVisibleAndClick('paymentMethods.mbway.denyButton', 'Clicking MBWay Deny button');
        console.log('MBWay Deny button clicked.');
    }

    /**
     * Handles actions after placing the order for negative scenarios.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        await this.clickDenyButton();

        // Disable steps that won't be relevant after denying the transaction
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);

        console.log('MBWay transaction denied as expected');
    }
}
