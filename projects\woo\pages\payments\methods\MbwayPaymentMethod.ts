import { PaymentMethod } from '@woo/services/payments';

export default class MbwayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.completeMBWayWalletSteps();
    }

    /**
     * Completes the wallet steps specific to MBWay.
     */
    protected async completeMBWayWalletSteps(): Promise<void> {
        await this.checkoutPage.completeWalletSteps();
        console.log('MBWay wallet steps completed.');
    }
}
