import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';

export default class MbwayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.clickApproveButton();
    }

    /**
     * Clicks the Approve button for MBWay payment.
     */
    protected async clickApproveButton(): Promise<void> {
        await wooPlugin.helper.waitForVisibleAndClick('paymentMethods.mbway.approveButton', 'Clicking MBWay Approve button');
        console.log('MBWay Approve button clicked.');
    }
}
