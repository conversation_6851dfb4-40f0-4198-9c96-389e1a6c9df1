import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';

export default class MbwayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.clickAllowButton();
    }

    /**
     * Clicks the Allow button for MBWay payment.
     */
    protected async clickAllowButton(): Promise<void> {
        await wooPlugin.helper.waitForVisibleAndClick('paymentMethods.mbway.allowButton', 'Clicking MBWay Allow button');
        console.log('MBWay Allow button clicked.');
    }
}
