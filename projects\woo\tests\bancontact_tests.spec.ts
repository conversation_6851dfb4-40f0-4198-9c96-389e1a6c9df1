import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('Bancontact Tests', () => {
    test('Place order with Bancontact', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'bancontact' },
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with Bancontact (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Bancontact (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
            products: [
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Bancontact (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Bancontact (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_bancontact', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: '15', card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_bancontact', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (Failed)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact', responseStatus: '490' },
        });
    });

    test('Place order with Bancontact (Rejected)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact', responseStatus: '690' },
        });
    });

    test('Place order with Bancontact (Canceled)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'bancontact', responseStatus: '890' },
        });
    });

    test('Place order and Refund with Bancontact with content type "json"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with Bancontact with content type "httppost"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await refundService.executeSteps();
    });
});
