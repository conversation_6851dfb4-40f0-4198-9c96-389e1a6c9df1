import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@woo/services/storefront';
import wooPlugin from '@woo/WooPlugin';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('MBWay Tests', () => {
    test('Place order with MBWay aaaa', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'flat',
        });
    });

    test('Place order with MBWay (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with MBWay (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Place order with MBWay (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with MBWay (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with MBWay (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('mbway', {
            enabled: 'yes',
            fee_fixed: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with MBWay (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('mbway', {
            enabled: 'yes',
            fee_percentage: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MBWAY,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
